name: Docker CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: habit_tracker_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r habit-tracker/requirements.txt

      - name: Lint with flake8
        run: |
          echo "Running flake8..."
          flake8 habit-tracker/app
          echo "Linting completed."

      - name: Run unit tests
        env:
          PYTHONPATH: .
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: habit_tracker_test
          DB_USER: test_user
          DB_PASSWORD: test_password
          SECRET_KEY: test-secret-key
        run: |
          echo "Running pytest..."
          cd habit-tracker
          pytest tests -v --cov=app --cov-report=xml
          echo "Unit tests completed."

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./habit-tracker/coverage.xml
          flags: unittests
          name: codecov-umbrella

  docker-build:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          target: production
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  docker-test:
    needs: docker-build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build test image
        run: |
          docker build -t habit-tracker-test --target development .

      - name: Start PostgreSQL container
        run: |
          docker run -d --name postgres-test \
            -e POSTGRES_DB=habit_tracker_test \
            -e POSTGRES_USER=test_user \
            -e POSTGRES_PASSWORD=test_password \
            -p 5432:5432 \
            postgres:15-alpine

      - name: Wait for PostgreSQL
        run: |
          docker exec postgres-test pg_isready -U test_user -d habit_tracker_test

      - name: Run tests in Docker container
        run: |
          docker run --rm \
            --network host \
            -e DB_HOST=localhost \
            -e DB_PORT=5432 \
            -e DB_NAME=habit_tracker_test \
            -e DB_USER=test_user \
            -e DB_PASSWORD=test_password \
            -e SECRET_KEY=test-secret-key \
            habit-tracker-test \
            bash -c "cd habit-tracker && python -m pytest tests -v"

      - name: Cleanup
        if: always()
        run: |
          docker stop postgres-test || true
          docker rm postgres-test || true

  security-scan:
    needs: test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  terraform-plan:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Terraform Init
        working-directory: terraform
        run: terraform init

      - name: Terraform Plan
        working-directory: terraform
        env:
          TF_VAR_flask_secret_key: ${{ secrets.FLASK_SECRET_KEY }}
          TF_VAR_domain_name: ${{ secrets.DOMAIN_NAME }}
          TF_VAR_ssl_email: ${{ secrets.SSL_EMAIL }}
          TF_VAR_github_token: ${{ secrets.GITHUB_TOKEN }}
          TF_VAR_github_username: ${{ github.actor }}
          TF_VAR_container_image_name: ${{ github.repository }}
        run: |
          terraform plan -var-file="environments/prod.tfvars" -out=tfplan
          terraform show -no-color tfplan > plan.txt

      - name: Comment PR with Terraform Plan
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const fs = require('fs');
            const plan = fs.readFileSync('terraform/plan.txt', 'utf8');
            const maxGitHubBodyCharacters = 65536;

            function chunkSubstr(str, size) {
              const numChunks = Math.ceil(str.length / size)
              const chunks = new Array(numChunks)
              for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
                chunks[i] = str.substr(o, size)
              }
              return chunks
            }

            const planChunks = chunkSubstr(plan, maxGitHubBodyCharacters);

            for (let i = 0; i < planChunks.length; i++) {
              const output = `### Terraform Plan Part ${i + 1}

            \`\`\`terraform
            ${planChunks[i]}
            \`\`\`

            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`, Working Directory: \`terraform\`, Workflow: \`${{ github.workflow }}\`*`;

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: output
              });
            }

  terraform-apply:
    needs: [docker-build, docker-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    outputs:
      vm_ip: ${{ steps.terraform-output.outputs.vm_ip }}
      ssh_private_key: ${{ steps.terraform-output.outputs.ssh_private_key }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"
          terraform_wrapper: false

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Terraform Init
        working-directory: terraform
        run: terraform init

      - name: Terraform Apply
        working-directory: terraform
        env:
          TF_VAR_flask_secret_key: ${{ secrets.FLASK_SECRET_KEY }}
          TF_VAR_domain_name: ${{ secrets.DOMAIN_NAME }}
          TF_VAR_ssl_email: ${{ secrets.SSL_EMAIL }}
          TF_VAR_github_token: ${{ secrets.GITHUB_TOKEN }}
          TF_VAR_github_username: ${{ github.actor }}
          TF_VAR_container_image_name: ${{ github.repository }}
        run: terraform apply -var-file="environments/prod.tfvars" -auto-approve

      - name: Get Terraform Outputs
        id: terraform-output
        working-directory: terraform
        run: |
          echo "vm_ip=$(terraform output -raw public_ip_address)" >> $GITHUB_OUTPUT
          echo "ssh_private_key<<EOF" >> $GITHUB_OUTPUT
          terraform output -raw ssh_private_key >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

  deploy:
    needs: terraform-apply
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ needs.terraform-apply.outputs.ssh_private_key }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ needs.terraform-apply.outputs.vm_ip }} >> ~/.ssh/known_hosts

      - name: Create deployment environment file
        run: |
          cat > .env.prod << EOF
          CONTAINER_IMAGE=ghcr.io/${{ github.repository }}:${{ github.sha }}
          SECRET_KEY=${{ secrets.FLASK_SECRET_KEY }}
          DB_HOST=${{ secrets.DB_HOST }}
          DB_PORT=5432
          DB_NAME=${{ secrets.DB_NAME }}
          DB_USER=${{ secrets.DB_USER }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}
          EOF

      - name: Copy deployment files to VM
        run: |
          scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no .env.prod azureuser@${{ needs.terraform-apply.outputs.vm_ip }}:/tmp/
          scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no scripts/deploy.sh azureuser@${{ needs.terraform-apply.outputs.vm_ip }}:/tmp/

      - name: Deploy application
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no azureuser@${{ needs.terraform-apply.outputs.vm_ip }} << 'EOF'
            # Load environment variables
            source /tmp/.env.prod

            # Login to GitHub Container Registry
            echo "${{ secrets.GITHUB_TOKEN }}" | sudo docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Pull the latest image
            sudo docker pull $CONTAINER_IMAGE

            # Stop existing container if running
            sudo docker stop habit-tracker-app || true
            sudo docker rm habit-tracker-app || true

            # Run the new container
            sudo docker run -d \
              --name habit-tracker-app \
              --restart unless-stopped \
              -p 5000:5000 \
              -e FLASK_ENV=production \
              -e SECRET_KEY="$SECRET_KEY" \
              -e DB_HOST="$DB_HOST" \
              -e DB_PORT="$DB_PORT" \
              -e DB_NAME="$DB_NAME" \
              -e DB_USER="$DB_USER" \
              -e DB_PASSWORD="$DB_PASSWORD" \
              $CONTAINER_IMAGE

            # Wait for application to start
            sleep 30

            # Check if application is healthy
            curl -f http://localhost:5000/ || exit 1

            echo "Application deployed successfully!"
          EOF

      - name: Setup SSL Certificate (if domain configured)
        if: ${{ secrets.DOMAIN_NAME != '' }}
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no azureuser@${{ needs.terraform-apply.outputs.vm_ip }} << 'EOF'
            # Update Nginx configuration with domain
            sudo sed -i "s/server_name _;/server_name ${{ secrets.DOMAIN_NAME }};/g" /etc/nginx/sites-available/habit-tracker

            # Test Nginx configuration
            sudo nginx -t

            # Reload Nginx
            sudo systemctl reload nginx

            # Obtain SSL certificate
            sudo certbot --nginx -d ${{ secrets.DOMAIN_NAME }} --email ${{ secrets.SSL_EMAIL }} --agree-tos --non-interactive

            echo "SSL certificate configured successfully!"
          EOF

      - name: Verify deployment
        run: |
          VM_IP=${{ needs.terraform-apply.outputs.vm_ip }}

          # Test HTTP (should redirect to HTTPS)
          echo "Testing HTTP redirect..."
          curl -I http://$VM_IP | grep -q "301\|302" || echo "Warning: HTTP redirect not working"

          # Test application health
          echo "Testing application health..."
          curl -f http://$VM_IP/ || curl -f https://$VM_IP/ || {
            echo "Application health check failed"
            exit 1
          }

          echo "Deployment verification completed successfully!"
          echo "Application is available at: http://$VM_IP"
          if [ -n "${{ secrets.DOMAIN_NAME }}" ]; then
            echo "Domain URL: https://${{ secrets.DOMAIN_NAME }}"
          fi

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa
          rm -f .env.prod

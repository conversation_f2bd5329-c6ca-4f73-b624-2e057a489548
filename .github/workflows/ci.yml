name: Docker CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: habit_tracker_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r habit-tracker/requirements.txt

      - name: Lint with flake8
        run: |
          echo "Running flake8..."
          flake8 habit-tracker/app
          echo "Linting completed."

      - name: Run unit tests
        env:
          PYTHONPATH: .
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: habit_tracker_test
          DB_USER: test_user
          DB_PASSWORD: test_password
          SECRET_KEY: test-secret-key
        run: |
          echo "Running pytest..."
          cd habit-tracker
          pytest tests -v --cov=app --cov-report=xml
          echo "Unit tests completed."

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./habit-tracker/coverage.xml
          flags: unittests
          name: codecov-umbrella

  docker-build:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          target: production
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  docker-test:
    needs: docker-build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build test image
        run: |
          docker build -t habit-tracker-test --target development .

      - name: Start PostgreSQL container
        run: |
          docker run -d --name postgres-test \
            -e POSTGRES_DB=habit_tracker_test \
            -e POSTGRES_USER=test_user \
            -e POSTGRES_PASSWORD=test_password \
            -p 5432:5432 \
            postgres:15-alpine

      - name: Wait for PostgreSQL
        run: |
          docker exec postgres-test pg_isready -U test_user -d habit_tracker_test

      - name: Run tests in Docker container
        run: |
          docker run --rm \
            --network host \
            -e DB_HOST=localhost \
            -e DB_PORT=5432 \
            -e DB_NAME=habit_tracker_test \
            -e DB_USER=test_user \
            -e DB_PASSWORD=test_password \
            -e SECRET_KEY=test-secret-key \
            habit-tracker-test \
            bash -c "cd habit-tracker && python -m pytest tests -v"

      - name: Cleanup
        if: always()
        run: |
          docker stop postgres-test || true
          docker rm postgres-test || true

  security-scan:
    needs: test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
